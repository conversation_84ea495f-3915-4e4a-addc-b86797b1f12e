{"name": "eventtts", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^4.30.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.2.2", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@stripe/stripe-js": "^4.1.0", "@types/qrcode": "^1.5.5", "@uploadthing/react": "6.7.2", "@vercel/speed-insights": "^1.0.12", "axios": "^1.12.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "gsap": "^3.13.0", "input-otp": "^1.4.2", "jspdf": "^3.0.2", "leaflet": "^1.9.4", "leaflet-routing-machine": "^3.2.12", "lucide-react": "^0.417.0", "mongodb": "^6.8.0", "mongoose": "^8.5.1", "next": "^15.5.3", "next-themes": "^0.3.0", "qrcode": "^1.5.4", "query-string": "^9.1.0", "react": "^18.3.1", "react-day-picker": "^9.9.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "react-icons": "^5.2.1", "react-leaflet": "^4.2.1", "react-resizable-panels": "^3.0.6", "recharts": "^3.2.0", "resend": "^6.1.0", "sonner": "^2.0.7", "stripe": "^16.5.0", "svix": "^1.25.0", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^6.13.2", "vaul": "^0.9.1", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@types/leaflet": "^1.9.20", "@types/leaflet-routing-machine": "^3.2.9", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "@types/recharts": "^2.0.1", "@types/xlsx": "^0.0.35", "autoprefixer": "^10.4.19", "postcss": "^8", "tailwindcss": "^3.4.7", "typescript": "^5"}}